'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Loading from '@/components/dentoui/Loading';
import AddPatientModal from '@/components/hastalar/AddPatientModal';
import ScanUploadModal from '@/components/hastalar/ScanUploadModal';
import ReportViewModal from '@/components/hastalar/ReportViewModal';
import { useAuth } from '@/lib/contexts/AuthContext';
import { usePatient } from '@/lib/hooks/usePatient';
import { useUser } from '@/lib/hooks/useUser';
import { Plus, Edit, Eye, Upload, User, Phone, Hospital, MapPin, Camera, Heart, Microscope, Image as ImageIcon, Folder, AlertCircle, FileText } from 'lucide-react';
import Image from 'next/image';
import DentoButtonSecondary from '@/components/dentoui/DentoButtonSecondary';
import Header from '@/components/dentoui/Header';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getCompletedIstemFormuByPatient } from '@/lib/services/istemFormuService';
import { getPatientImages } from '@/lib/services/patientImageService';
import { IstemFormuData, PatientImage } from '@/lib/types';

interface PatientFormData {
  id?: string;
  firstName: string;
  lastName: string;
  tcKimlik: string;
  birthDate: Date | undefined;
  gender: string;
  phone: string;
  email?: string;
}

export default function PatientDetail() {
  const params = useParams();
  const patientId = Array.isArray(params.id) ? params.id[0] : params.id;
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [activeTab, setActiveTab] = useState('images');
  const [imageTypeFilter, setImageTypeFilter] = useState('Tüm Türler');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isScanUploadModalOpen, setIsScanUploadModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [selectedImageForReport, setSelectedImageForReport] = useState<PatientImage | null>(null);
  const [completedForms, setCompletedForms] = useState<IstemFormuData[]>([]);
  const [standaloneImages, setStandaloneImages] = useState<PatientImage[]>([]);
  
  // useAuth handles redirecting unauthenticated users to login when options are provided
  const { loading: authLoading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  // Get admin status
  const { isAdmin } = useUser();

  // Fetch patient data from backend
  const { patient, loading: patientLoading, error: patientError, updatePatientData } = usePatient(patientId || null);

  const loading = authLoading || patientLoading;

  const fetchCompletedForms = useCallback(async () => {
    if (!patientId) return;
    try {
      const forms = await getCompletedIstemFormuByPatient(patientId);
      setCompletedForms(forms);
    } catch (error) {
      console.error("Error fetching completed forms:", error);
      toast.error('Tamamlanmış formlar yüklenirken bir hata oluştu.');
    }
  }, [patientId]);

  const fetchStandaloneImages = useCallback(async () => {
    if (!patientId) return;
    try {
      const images = await getPatientImages(patientId);
      setStandaloneImages(images);
    } catch (error) {
      console.error("Error fetching standalone images:", error);
      toast.error('Görüntüler yüklenirken bir hata oluştu.');
    }
  }, [patientId]);

  const refreshImageData = () => {
    fetchCompletedForms();
    fetchStandaloneImages();
  };

  const handleViewReport = (image: PatientImage) => {
    setSelectedImageForReport(image);
    setIsReportModalOpen(true);
  };

  useEffect(() => {
    if (patientId) {
      fetchCompletedForms();
      fetchStandaloneImages();
    }
  }, [patientId, fetchCompletedForms, fetchStandaloneImages]);

  if (loading) {
    return <Loading message="Hasta detayları yükleniyor..." />;
  }

  // Handle patient not found or error
  if (patientError || !patient) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar />
        <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
          isSidebarExpanded ? 'ml-64' : 'ml-16'
        }`}>
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {patientError || 'Hasta bulunamadı'}
              </h2>
              <p className="text-gray-600 mb-4">
                {patientError ? 'Hasta bilgileri yüklenirken bir hata oluştu.' : 'Aradığınız hasta mevcut değil.'}
              </p>
              <DentoButtonSecondary
                onClick={() => window.history.back()}
                bgColor="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                textColor="text-white"
              >
                Geri Dön
              </DentoButtonSecondary>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Helper functions to format patient data
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Get patient initials
  const getPatientInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
  };

  // Image statistics
  const allImages = completedForms.flatMap(form => form.images || []);
  const allStandaloneImages = standaloneImages.map(img => img.imageUrl);
  const totalImages = allImages.length + allStandaloneImages.length;

  // Count images by type from completed forms
  const imageCountsByType = allImages.reduce((acc, imageUrl) => {
    try {
      const urlParts = imageUrl.split('%2F');
      const filenameWithQuery = urlParts[urlParts.length - 1];
      const filename = filenameWithQuery.split('?')[0];
      const type = decodeURIComponent(filename.split('_')[0]);
      if (type) {
        acc[type] = (acc[type] || 0) + 1;
      }
    } catch (e) {
      console.error("Could not parse image URL for type", imageUrl, e);
    }
    return acc;
  }, {} as { [key: string]: number });

  // Add standalone images to type counts
  standaloneImages.forEach(img => {
    if (img.scanType) {
      imageCountsByType[img.scanType] = (imageCountsByType[img.scanType] || 0) + 1;
    } else {
      imageCountsByType['Diğer'] = (imageCountsByType['Diğer'] || 0) + 1;
    }
  });

  interface StatCardStyle {
    bg: string;
    border: string;
    iconBg: string;
    valueColor: string;
    titleColor: string;
    line: string;
    icon: React.ReactNode;
  }

  const statCardStyleMap: { [key: string]: StatCardStyle } = {
    total: { bg: 'from-blue-50 via-blue-100 to-blue-200', border: 'border-blue-200/50', iconBg: 'from-blue-500 to-blue-600', valueColor: 'text-blue-700', titleColor: 'text-blue-800', line: 'from-blue-400 to-blue-600', icon: <Camera className="w-8 h-8 text-white" /> },
    panoramik: { bg: 'from-emerald-50 via-emerald-100 to-emerald-200', border: 'border-emerald-200/50', iconBg: 'from-emerald-500 to-emerald-600', valueColor: 'text-emerald-700', titleColor: 'text-emerald-800', line: 'from-emerald-400 to-emerald-600', icon: <Heart className="w-8 h-8 text-white" /> },
    cbct: { bg: 'from-purple-50 via-purple-100 to-purple-200', border: 'border-purple-200/50', iconBg: 'from-purple-500 to-purple-600', valueColor: 'text-purple-700', titleColor: 'text-purple-800', line: 'from-purple-400 to-purple-600', icon: <Microscope className="w-8 h-8 text-white" /> },
    intraoral: { bg: 'from-orange-50 via-orange-100 to-orange-200', border: 'border-orange-200/50', iconBg: 'from-orange-500 to-orange-600', valueColor: 'text-orange-700', titleColor: 'text-orange-800', line: 'from-orange-400 to-orange-600', icon: <ImageIcon className="w-8 h-8 text-white" /> },
    default: { bg: 'from-gray-50 via-gray-100 to-gray-200', border: 'border-gray-200/50', iconBg: 'from-gray-500 to-gray-600', valueColor: 'text-gray-700', titleColor: 'text-gray-800', line: 'from-gray-400 to-gray-600', icon: <ImageIcon className="w-8 h-8 text-white" /> }
  };

  const getStyleForType = (type: string) => {
    const cleanType = type.toLowerCase();
    return statCardStyleMap[cleanType as keyof typeof statCardStyleMap] || statCardStyleMap.default;
  };

  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard' },
    { label: 'Hastalar', href: '/dashboard/hastalar' },
    { label: `${patient.firstName} ${patient.lastName}`, href: `/dashboard/hastalar/${patient.id}`, isActive: true },
  ];

  const headerActions = (
    <>
      {isAdmin && (
        <DentoButtonSecondary
          onClick={() => setIsScanUploadModalOpen(true)}
          icon={<Plus className="w-5 h-5" />}
          bgColor="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700"
          textColor="text-white"
          iconAnimation="group-hover:rotate-90"
          className="shadow-lg hover:shadow-xl"
        >
          Yeni Görüntü
        </DentoButtonSecondary>
      )}
      <DentoButtonSecondary
        onClick={() => setIsEditModalOpen(true)}
        icon={<Edit className="w-5 h-5" />}
        bgColor="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
        textColor="text-white"
        iconAnimation="group-hover:rotate-12"
        className="shadow-lg hover:shadow-xl"
      >
        Düzenle
      </DentoButtonSecondary>
    </>
  );

  // Convert patient data to form format
  const getPatientFormData = (): PatientFormData => {
    return {
      id: patient.id,
      firstName: patient.firstName,
      lastName: patient.lastName,
      tcKimlik: patient.tcKimlik,
      birthDate: patient.birthDate,
      gender: patient.gender,
      phone: patient.phone,
      email: patient.email
    };
  };

  const handleEditPatient = async (updatedPatientData: PatientFormData) => {
    try {
      if (!patient?.id) return;
      
      // Convert PatientFormData to Patient update data
      const updateData = {
        firstName: updatedPatientData.firstName,
        lastName: updatedPatientData.lastName,
        tcKimlik: updatedPatientData.tcKimlik,
        phone: updatedPatientData.phone,
        gender: updatedPatientData.gender,
        email: updatedPatientData.email,
        birthDate: updatedPatientData.birthDate,
      };

      await updatePatientData(patient.id, updateData);
      setIsEditModalOpen(false);
      toast.success('Hasta bilgileri başarıyla güncellendi');
    } catch (error) {
      console.error('Error updating patient:', error);
      toast.error('Hasta bilgileri güncellenirken bir hata oluştu');
    }
  };

  // Combine images from completed forms and standalone images
  const formImages = completedForms.flatMap(form =>
    form.images?.map((imageUrl, index) => ({
      id: `form-${form.id}-${index}`,
      title: form.xrayTypes.join(', '),
      date: form.completedAt ? formatDate(form.completedAt) : 'Bilinmiyor',
      doctor: 'İstem Formu',
      type: form.xrayTypes[0] || 'Unknown',
      typeColor: 'blue',
      thumbnail: imageUrl,
      source: 'form' as const,
      hasReport: false, // Form images don't have reports
      originalImage: undefined // Form images don't have original image reference
    })) || []
  );

  const standaloneImagesList = standaloneImages.map((img, index) => ({
    id: `standalone-${img.id}-${index}`,
    title: img.scanType || 'Doğrudan Yükleme',
    date: formatDate(img.createdAt),
    doctor: 'Doğrudan Yükleme',
    type: img.scanType || 'Diğer',
    typeColor: 'emerald' as const,
    thumbnail: img.imageUrl,
    source: 'standalone' as const,
    notes: img.notes,
    hasReport: !!img.reportType,
    reportType: img.reportType,
    originalImage: img // Keep reference to original image for report viewing
  }));

  const images = [...formImages, ...standaloneImagesList];

  const imageStats = [
    { title: 'Toplam Görüntü', value: totalImages, type: 'total' },
    ...Object.entries(imageCountsByType).map(([type, count]) => ({
      title: type,
      value: count,
      type: type
    }))
  ];

  const getStatusBadge = (status: string, color: string) => {
    const colorClasses = {
      green: 'bg-green-100 text-green-800',
      orange: 'bg-orange-100 text-orange-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      emerald: 'bg-emerald-100 text-emerald-800'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${colorClasses[color as keyof typeof colorClasses]}`}>
        {status}
      </span>
    );
  };



  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="Hasta Detayı"
          description="Hasta bilgileri ve radyolojik görüntüler"
          breadcrumbs={breadcrumbs}
          rightComponent={headerActions}
        />

        {/* Main Content Area */}
        <div className="p-8">
          {/* Patient Information Card */}
          <div className="group bg-gradient-to-br from-white via-white to-blue-50/30 rounded-2xl shadow-xl border border-gray-200/50 p-10 mb-10 hover:shadow-2xl transition-all duration-500 backdrop-blur-sm">
            <div className="flex items-start justify-between mb-8">
              <div className="flex items-center space-x-8">
                <div className="relative">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 via-blue-500 to-blue-600 flex items-center justify-center shadow-xl ring-4 ring-white/50">
                    <span className="text-3xl font-bold text-white drop-shadow-lg">{getPatientInitials(patient.firstName, patient.lastName)}</span>
                  </div>
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-emerald-400 rounded-xl flex items-center justify-center shadow-lg">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                  </div>
                </div>
                <div>
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent mb-3 tracking-tight">
                    {patient.firstName} {patient.lastName}
                  </h2>
                  <div className="flex items-center space-x-3">
                    <div className="h-1 w-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                    <span className="text-gray-500 font-medium">Hasta Profili</span>
                  </div>
                </div>
              </div>
              <div className="transform hover:scale-110 transition-transform duration-300">
                {getStatusBadge('Aktif', 'green')}
              </div>
            </div>

            {/* Compact Patient Info Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {/* TC Kimlik */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <User className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">TC</span>
                </div>
                <p className="text-sm font-bold text-gray-900 truncate">{patient.tcKimlik}</p>
              </div>

              {/* Age */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-xs font-bold text-white">Y</span>
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">Yaş</span>
                </div>
                <p className="text-sm font-bold text-gray-900">{patient.yas || 'Belirtilmemiş'}</p>
              </div>

              {/* Gender */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-xs font-bold text-white">C</span>
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">Cinsiyet</span>
                </div>
                <p className="text-sm font-bold text-gray-900">{patient.gender}</p>
              </div>

              {/* Phone */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <Phone className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">Telefon</span>
                </div>
                <p className="text-sm font-bold text-gray-900 truncate">{patient.phone}</p>
              </div>

              {/* Email */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-xs font-bold text-white">@</span>
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">Email</span>
                </div>
                <p className="text-sm font-bold text-gray-900 truncate">{patient.email || 'Belirtilmemiş'}</p>
              </div>

              {/* Address */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <MapPin className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">Adres</span>
                </div>
                <p className="text-sm font-bold text-gray-900 truncate">{patient.address || 'Belirtilmemiş'}</p>
              </div>
            </div>

            {/* Medical Info Row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              {/* First Registration */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <Hospital className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">İlk Kayıt</span>
                </div>
                <p className="text-sm font-bold text-gray-900">{formatDate(patient.createdAt)}</p>
              </div>

              {/* Total Procedures */}
              <div className="group bg-gradient-to-br from-blue-50 to-blue-100/80 rounded-xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-xs font-bold text-white">#</span>
                  </div>
                  <span className="text-xs font-bold text-blue-700 uppercase tracking-wide">Toplam İşlem</span>
                </div>
                <p className="text-sm font-bold text-gray-900">{patient.totalProcedures || 0}</p>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-gradient-to-br from-white via-white to-slate-50/30 rounded-2xl shadow-xl border border-gray-200/50 mb-10 overflow-hidden backdrop-blur-sm">
            <div className="border-b border-gray-200/60 bg-gradient-to-r from-slate-50/50 to-transparent">
              <nav className="flex space-x-2 px-8 py-2">
                <button
                  onClick={() => setActiveTab('images')}
                  className={`relative py-4 px-6 font-semibold text-sm cursor-pointer transition-all duration-300 rounded-xl ${
                    activeTab === 'images'
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  }`}
                >
                  <span className="relative z-10">Radyolojik Görüntüler</span>
                  {activeTab === 'images' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl blur-sm opacity-50"></div>
                  )}
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className={`relative py-4 px-6 font-semibold text-sm cursor-pointer transition-all duration-300 rounded-xl ${
                    activeTab === 'history'
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  }`}
                >
                  <span className="relative z-10">İşlem Geçmişi</span>
                  {activeTab === 'history' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl blur-sm opacity-50"></div>
                  )}
                </button>
                <button
                  onClick={() => setActiveTab('reports')}
                  className={`relative py-4 px-6 font-semibold text-sm cursor-pointer transition-all duration-300 rounded-xl ${
                    activeTab === 'reports'
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  }`}
                >
                  <span className="relative z-10">Raporlar</span>
                  {activeTab === 'reports' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl blur-sm opacity-50"></div>
                  )}
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-8">
              {activeTab === 'images' && (
                <div>
                  {/* Image Type Summary Cards */}
                  <div className="mt-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {imageStats.map((stat) => {
                        const style = getStyleForType(stat.type);
                        return (
                          <div key={stat.title} className={`group bg-gradient-to-br ${style.bg} rounded-2xl p-8 text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105 border ${style.border}`}>
                            <div className={`w-16 h-16 bg-gradient-to-br ${style.iconBg} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow`}>
                              {style.icon}
                            </div>
                            <div className={`text-4xl font-bold ${style.valueColor} mb-2 group-hover:scale-110 transition-transform`}>{stat.value}</div>
                            <div className={`font-bold text-lg ${style.titleColor}`}>{stat.title}</div>
                            <div className={`h-1 w-12 bg-gradient-to-r ${style.line} rounded-full mx-auto mt-3 opacity-60`}></div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Image Gallery Header */}
                  <div className="mt-8 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center shadow-lg">
                        <Folder className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">Görüntü Dosyaları</h3>
                        <p className="text-gray-500 font-medium">Radyolojik görüntü arşivi</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Select
                        value={imageTypeFilter}
                        onValueChange={setImageTypeFilter}
                      >
                        <SelectTrigger className="w-40 px-5 py-6 h-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer bg-white shadow-sm font-medium text-gray-700">
                          <SelectValue placeholder="Görüntü türü seçiniz" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Tüm Türler">Tüm Türler</SelectItem>
                          <SelectItem value="X-Ray">X-Ray</SelectItem>
                          <SelectItem value="Panoramik">Panoramik</SelectItem>
                          <SelectItem value="CBCT">CBCT</SelectItem>
                          <SelectItem value="Intraoral">Intraoral</SelectItem>
                        </SelectContent>
                      </Select>
                      {isAdmin && (
                        <button 
                          onClick={() => setIsScanUploadModalOpen(true)}
                          className="group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-105"
                        >
                          <Upload className="w-5 h-5 group-hover:scale-110 transition-transform" />
                          <span className="font-semibold">Yükle</span>
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Image Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
                    {images.map((image) => (
                      <div key={image.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300">
                        <div className="relative aspect-video bg-gray-100 flex items-center justify-center">
                          <Image
                            src={image.thumbnail}
                            alt={image.title}
                            fill
                            className="object-cover bg-gray-100"
                          />
                          {/* Report indicator */}
                          {image.hasReport && (
                            <div className="absolute top-2 right-2">
                              <div className="flex items-center space-x-1 bg-blue-600 text-white px-2 py-1 rounded-lg text-xs font-medium shadow-lg">
                                <FileText className="w-3 h-3" />
                                <span>Rapor</span>
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">{image.title}</h4>
                              <p className="text-sm text-gray-500">{image.date}</p>
                              <p className="text-sm text-gray-600">{image.doctor}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getStatusBadge(image.type, image.typeColor)}
                              {image.hasReport && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full" title="Rapor mevcut"></div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => window.open(image.thumbnail, '_blank')}
                              className="flex items-center justify-center w-8 h-8 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-lg transition-colors cursor-pointer"
                              title="Görüntüyü görüntüle"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            {image.hasReport && image.originalImage && (
                              <button
                                onClick={() => handleViewReport(image.originalImage)}
                                className="flex items-center justify-center w-8 h-8 bg-green-100 hover:bg-green-200 text-green-600 rounded-lg transition-colors cursor-pointer"
                                title="Raporu görüntüle"
                              >
                                <FileText className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'history' && (
                <div className="text-center py-12">
                  <p className="text-gray-500">İşlem geçmişi içeriği burada görüntülenecek.</p>
                </div>
              )}

              {activeTab === 'reports' && (
                <div className="text-center py-12">
                  <p className="text-gray-500">Raporlar içeriği burada görüntülenecek.</p>
                </div>
              )}
            </div>
          </div>


        </div>
      </div>

      {/* Edit Patient Modal */}
      <AddPatientModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSubmit={handleEditPatient}
        patientData={getPatientFormData()}
        isEditing={true}
      />

      {/* Scan Upload Modal */}
      <ScanUploadModal
        isOpen={isScanUploadModalOpen}
        onClose={() => setIsScanUploadModalOpen(false)}
        patient={patient}
        onUploadComplete={refreshImageData}
      />

      {/* Report View Modal */}
      {selectedImageForReport && (
        <ReportViewModal
          isOpen={isReportModalOpen}
          onClose={() => {
            setIsReportModalOpen(false);
            setSelectedImageForReport(null);
          }}
          image={selectedImageForReport}
          onUpdate={refreshImageData}
        />
      )}
    </div>
  );
}