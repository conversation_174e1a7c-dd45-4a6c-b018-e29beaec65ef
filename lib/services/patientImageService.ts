import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  query,
  where,
  orderBy,
} from 'firebase/firestore';

// Import PatientImage from types to maintain consistency
import { PatientImage } from '../types';

/**
 * Add a standalone patient image record
 */
export const addPatientImage = async (imageData: Omit<PatientImage, 'id' | 'createdAt'>): Promise<string> => {
  try {
    const patientImagesCollection = collection(db, 'patient-images');

    // Filter out undefined values to prevent Firestore errors
    const processedData: any = {
      patientId: imageData.patientId,
      uploadedBy: imageData.uploadedBy,
      imageUrl: imageData.imageUrl,
      imagePath: imageData.imagePath,
      filename: imageData.filename,
      createdAt: new Date(),
    };

    // Only add optional fields if they have valid values
    if (imageData.scanType !== undefined && imageData.scanType !== '') {
      processedData.scanType = imageData.scanType;
    }

    if (imageData.notes !== undefined && imageData.notes !== '') {
      processedData.notes = imageData.notes;
    }

    // Add report fields if they exist
    if (imageData.reportType !== undefined) {
      processedData.reportType = imageData.reportType;
    }

    if (imageData.reportContent !== undefined && imageData.reportContent !== '') {
      processedData.reportContent = imageData.reportContent;
    }

    if (imageData.reportUrl !== undefined && imageData.reportUrl !== '') {
      processedData.reportUrl = imageData.reportUrl;
    }

    if (imageData.reportPath !== undefined && imageData.reportPath !== '') {
      processedData.reportPath = imageData.reportPath;
    }

    if (imageData.reportFilename !== undefined && imageData.reportFilename !== '') {
      processedData.reportFilename = imageData.reportFilename;
    }

    const docRef = await addDoc(patientImagesCollection, processedData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding patient image: ', error);
    throw new Error('Failed to add patient image');
  }
};

/**
 * Get all standalone images for a specific patient
 */
export const getPatientImages = async (patientId: string): Promise<PatientImage[]> => {
  try {
    const patientImagesCollection = collection(db, 'patient-images');
    const imagesQuery = query(
      patientImagesCollection,
      where('patientId', '==', patientId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(imagesQuery);
    const images = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as PatientImage[];
    
    return images;
  } catch (error) {
    console.error('Error fetching patient images: ', error);
    throw new Error('Failed to fetch patient images');
  }
};

/**
 * Update a patient image record (mainly for report updates)
 */
export const updatePatientImage = async (imageId: string, updateData: Partial<PatientImage>): Promise<void> => {
  try {
    const docRef = doc(db, 'patient-images', imageId);

    // Filter out undefined values
    const processedData: any = {};

    Object.keys(updateData).forEach(key => {
      const value = (updateData as any)[key];
      if (value !== undefined && value !== '') {
        processedData[key] = value;
      }
    });

    await updateDoc(docRef, processedData);
  } catch (error) {
    console.error('Error updating patient image:', error);
    throw new Error('Failed to update patient image');
  }
};

/**
 * Delete a patient image record
 */
export const deletePatientImage = async (imageId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'patient-images', imageId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting patient image:', error);
    throw new Error('Failed to delete patient image');
  }
};

/**
 * Get images by upload user (for admin purposes)
 */
export const getImagesByUploader = async (uploaderId: string): Promise<PatientImage[]> => {
  try {
    const patientImagesCollection = collection(db, 'patient-images');
    const imagesQuery = query(
      patientImagesCollection,
      where('uploadedBy', '==', uploaderId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(imagesQuery);
    const images = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as PatientImage[];
    
    return images;
  } catch (error) {
    console.error('Error fetching images by uploader: ', error);
    throw new Error('Failed to fetch images by uploader');
  }
};
